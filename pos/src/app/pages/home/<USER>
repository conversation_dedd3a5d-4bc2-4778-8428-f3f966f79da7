import { ChangeDetectorRef, Component, ViewChild, ElementRef, AfterViewInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { TypeSenseService } from '../../services/typesense.service';
import { FreebieService } from '../../services/freebie.service';
import { Product, CartItem, ProductSearchResult } from '../../models';
import { FreebieProduct } from '../../models/freebie.model';
import { TableComponent } from "src/app/components/table/table";
import { AutoCompleteModule } from 'primeng/autocomplete';
import { IftaLabelModule } from 'primeng/iftalabel';
import { TabsModule } from 'primeng/tabs';
import { BillingComponent } from 'src/app/components/billing/billing';
import { CartCalculationUtils } from 'src/app/utils/cart-calculation.utils';
import { FreebieManager, BillingTab } from '../../utils/freebie-manager';
import { IonicModule } from '@ionic/angular';

@Component({
  selector: 'app-home',
  templateUrl: 'home.page.html',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    BillingComponent,
    TableComponent,
    AutoCompleteModule,
    IftaLabelModule,
    TabsModule,
    IonicModule
  ],
})
export class HomePage implements AfterViewInit {
  @ViewChild('searchInput') searchInput!: ElementRef;
  currentTab = 0;
  billingTabs = [
    {
      title: 'Billing',
      value: 0,
      items: [] as CartItem[],
      // Tab-specific freebie properties
      showFreebies: false,
      freebiesProducts: [] as FreebieProduct[],
      currentCartAmount: 0,
      addedFreebies: new Set<string>()
    }
  ];
  searchSuggestions: Product[] = [];
  searchText: string = '';
  productsColumns: any[] = [];

  // Global freebie columns (shared across all tabs)
  freebiesColumns: any[] = [];

  private freebieManager: FreebieManager;

  constructor(
    public typesenseService: TypeSenseService,
    private freebieService: FreebieService,
    private cdr: ChangeDetectorRef,
  ) {
    this.freebieManager = new FreebieManager(this.freebieService);
  }

  ionViewDidEnter() {
    this.searchSuggestions = [];
    this.currentTab = 0;
    this.searchText = '';
    this.productsColumns = [
      { field: 'thumbnail_image', header: 'Image', type: 'image', width: '100px' },
      {
        field: 'name',
        header: 'Item Name',
        width: '100px',
        body: (item: any) => item.is_freebie ? `${item.name} (FREE)` : item.name,
        class: (item: any) => item.is_freebie ? 'text-green-600 font-semibold' : ''
      },
      { field: 'variant_name', header: 'Variant Name', width: '150px' },
      { field: 'child_sku', header: 'SKU', width: '100px' },
      {
        field: 'selling_price',
        header: 'Price',
        body: (item: any) => item.is_freebie ? 'FREE' : CartCalculationUtils.formatCurrency(item.selling_price),
        class: (item: any) => item.is_freebie ? 'text-green-600 font-semibold' : 'text-orange-600 font-semibold',
        width: '100px'
      },
      { field: 'discount', header: 'Discount', body: (item: any) => CartCalculationUtils.formatCurrency(CartCalculationUtils.calculateItemDiscount(item)), width: '100px' },
      { field: 'total_amount', header: 'Total Amt', body: (item: any) => CartCalculationUtils.formatCurrency(CartCalculationUtils.calculateItemTotal(item)), width: '100px' },
      {
        field: 'quantity',
        header: 'Quantity',
        type: 'quantity',
        width: '100px',
        disabled: (item: any) => item.is_freebie // Disable quantity editing for freebies
      },
      { header: 'Actions', type: 'action', buttons: [{ icon: 'pi pi-trash', id: 'remove', rounded: true, outlined: true, severity: 'danger' }], width: '50px' },
    ];

    // Initialize freebies columns
    this.freebiesColumns = [
      { field: 'thumbnail_image', header: 'Image', type: 'image', width: '80px' },
      { field: 'name', header: 'Item Name', width: '150px' },
      { field: 'variant_name', header: 'Variant', width: '100px' },
      { field: 'amount', header: 'Min Amount', body: (item: any) => CartCalculationUtils.formatCurrency(item.amount), class: 'text-green-600 font-semibold', width: '100px' },
      { field: 'available_qty', header: 'Available', width: '80px' },
      {
        header: 'Actions',
        type: 'action',
        buttons: [{
          icon: 'pi pi-plus',
          id: 'add_freebie',
          rounded: true,
          outlined: true,
          severity: 'success',
          label: 'Add',
          hide: (item: any) => this.freebieService.isFreebieInCart(this.billingTabs[this.currentTab].items, item.id) || this.billingTabs[this.currentTab].currentCartAmount < item.amount
        }],
        width: '80px'
      },
    ];

    // Initialize freebies
    this.initializeFreebies();
  }

  ngAfterViewInit() {
    setTimeout(() => {
      this.focusSearch();
    }, 100);
  }

  focusSearch() {
    if (this.searchInput?.nativeElement) {
      const inputElement = this.searchInput.nativeElement.querySelector('input');
      if (inputElement) {
        inputElement.focus();
        inputElement.setSelectionRange(
          inputElement.value.length,
          inputElement.value.length,
        );
      }
    }
  }

  onSearch(event: { query: string }) {
    const query = event.query;
    if (!query || query.trim() === '') {
      this.searchSuggestions = [];
      return;
    }
    this.typesenseService.searchProductsDirectly(query.trim()).then((result: ProductSearchResult) => {
      this.searchSuggestions = result.products || [];
    }).catch((error: any) => {
      console.error('Search error:', error);
      this.searchSuggestions = [];
    });
  }

  onEnterKey(event: any) {
    if (this.searchSuggestions?.length > 0) {
      event.preventDefault();
      event.stopPropagation();
      this.onSearchSelect({ value: this.searchSuggestions[0] });
    }
  }

  onSearchSelect(event: { value: Product } | Product) {
    const selectedProduct = 'value' in event ? event.value : event;
    if (selectedProduct) {
      this.addToCart(selectedProduct);
      this.searchSuggestions = [];
      this.searchText = '';
      setTimeout(() => {
        this.focusSearch();
      }, 100);
    }
  }

  onCartChange(_cartItems: CartItem[]) {
    this.refreshDisplayAndDetectChanges();
    this.checkCartAmountAndUpdateFreebies();
  }

  onCartCleared() {
    const currentTab = this.billingTabs[this.currentTab] as BillingTab;
    this.freebieManager.clearFreebiesOnCartClear(currentTab);
    this.refreshFreebiesDisplay();
  }

  billingGrandTotal() {
    return this.billingTabs[this.currentTab].items.reduce((total, item) =>
      total + ((item.selling_price || 0)) * (item.quantity || 0), 0
    );
  }

  // Initialize freebies on page load
  async initializeFreebies() {
    const currentTab = this.billingTabs[this.currentTab] as BillingTab;
    await this.freebieManager.initializeFreebies(currentTab, this.billingGrandTotal());
    this.refreshFreebiesDisplay();
  }

  // Check cart amount and update freebies when cart changes
  async checkCartAmountAndUpdateFreebies() {
    const currentTab = this.billingTabs[this.currentTab] as BillingTab;
    const updated = await this.freebieManager.checkCartAmountAndUpdate(currentTab, this.billingGrandTotal());
    if (updated) {
      this.refreshFreebiesDisplay();
    }
  }

  // Refresh freebies display to update button states
  private refreshFreebiesDisplay() {
    const currentTab = this.billingTabs[this.currentTab];
    // Create a new array reference to trigger change detection
    currentTab.freebiesProducts = [...currentTab.freebiesProducts];
    this.cdr.detectChanges();
  }

  addToCart(product: Product) {
    const manualQuantity = product.quantity && product.quantity > 0 ? product.quantity : 1;
    this.updateCartItemQuantity(product, manualQuantity, true);
    product.quantity = 1;
    this.refreshDisplayAndDetectChanges();
    // Check for freebies after adding product
    this.checkCartAmountAndUpdateFreebies();
  }

  private updateCartItemQuantity(product: Product | string, quantity: number, isAddition: boolean = false) {
    const productSku = typeof product === 'string' ? product : product.child_sku;
    const cartItem = this.billingTabs[this.currentTab].items.find(
      (item: any) => item.child_sku === productSku,
    );

    if (cartItem) {
      if (isAddition) {
        cartItem.quantity += quantity;
      } else {
        cartItem.quantity = quantity;
        if (cartItem.quantity <= 0) {
          this.removeFromCart({ child_sku: productSku });
          return;
        }
      }
    } else if (isAddition && typeof product === 'object') {
      const newCartItem = { ...product, quantity: quantity, tax: product.tax };
      this.billingTabs[this.currentTab].items.push(newCartItem);
    }
  }

  private refreshDisplayAndDetectChanges() {
    this.updateDisplayedProductsFromCart();
    this.cdr.detectChanges();
  }

  removeFromCart(product: { child_sku: string }) {
    const currentTab = this.billingTabs[this.currentTab] as BillingTab;
    if (!currentTab.items) return;

    const index = currentTab.items.findIndex((item: any) => item.child_sku === product.child_sku);
    if (index === -1) return;

    const removedItem = currentTab.items[index];
    this.freebieManager.handleItemRemoval(currentTab, removedItem);

    currentTab.items.splice(index, 1);
    this.refreshDisplayAndDetectChanges();
    this.refreshFreebiesDisplay();
  }

  updateDisplayedProductsFromCart() {
    if (this.billingTabs[this.currentTab].items && this.billingTabs[this.currentTab].items.length > 0) {
      this.billingTabs[this.currentTab].items = [...this.billingTabs[this.currentTab].items].map(item => ({
        ...item,
        quantity: item.quantity
      }));
    } else {
      this.removeTab(this.currentTab);
    }
  }
  onChange(ev: any) {
    const { event, rowData, column } = ev;
    if (column.field === 'quantity') {
      // Don't allow quantity changes for freebies
      if ((rowData as any).is_freebie) {
        console.warn('Cannot change quantity of freebie items');
        return;
      }

      rowData.quantity = event.value;
      this.updateCartItemQuantity(rowData, event.value, false);
      this.refreshDisplayAndDetectChanges();
      // Check for freebies after quantity change
      this.checkCartAmountAndUpdateFreebies();
    }
  }
  onActionClick(ev: any) {
    const { button, rowData } = ev;
    if (button.id === 'remove') {
      this.removeFromCart(rowData);
    } else if (button.id === 'add_freebie') {
      this.addFreebieManually(rowData);
    }
  }

  // Manually add freebie from the freebies table
  private addFreebieManually(freebie: FreebieProduct) {
    const currentTab = this.billingTabs[this.currentTab] as BillingTab;

    if (this.freebieManager.addFreebieManually(currentTab, freebie)) {
      this.refreshDisplayAndDetectChanges();
      this.refreshFreebiesDisplay();
    }
  }

  onTabChange(ev: any) {
    this.currentTab = ev;
    // Initialize freebies for the newly selected tab
    this.initializeFreebies();
  }
  removeTab(index: number) {
    if (this.billingTabs.length === 1) {
      // Clear cart items and freebies
      this.billingTabs[0].items = [];
      this.freebieManager.clearFreebiesOnCartClear(this.billingTabs[0] as BillingTab);
      this.refreshFreebiesDisplay();
    } else {
      this.billingTabs.splice(index, 1);
    }
    this.currentTab = index === 0 ? 0 : this.billingTabs?.length === index ? index - 1 : index;
  }
  addTab() {
    this.billingTabs.push({
      title: 'Billing',
      value: this.billingTabs.length,
      items: [],
      // Initialize tab-specific freebie properties
      showFreebies: false,
      freebiesProducts: [],
      currentCartAmount: 0,
      addedFreebies: new Set<string>()
    });
    this.currentTab = this.billingTabs.length - 1;
    // Initialize freebies for the new tab (should be empty since cart is empty)
    this.initializeFreebies();
  }
}
