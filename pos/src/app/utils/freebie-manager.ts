import { FreebieService } from '../services/freebie.service';
import { FreebieProduct } from '../models/freebie.model';
import { CartItem } from '../models/product.model';

export interface BillingTab {
  items: CartItem[];
  freebiesProducts: FreebieProduct[];
  showFreebies: boolean;
  currentCartAmount: number;
  addedFreebies: Set<string>;
}

export class FreebieManager {
  
  constructor(private freebieService: FreebieService) {}

  /**
   * Initialize freebies for a tab
   */
  async initializeFreebies(tab: BillingTab, cartAmount: number): Promise<void> {
    tab.currentCartAmount = cartAmount;
    await this.updateFreebies(tab);
    // Removed automatic freebie addition - only manual selection allowed
  }

  /**
   * Update freebies display for a tab
   */
  async updateFreebies(tab: BillingTab): Promise<void> {
    const freebies = await this.freebieService.getAvailableFreebies(tab.currentCartAmount);
    tab.freebiesProducts = freebies;
    tab.showFreebies = freebies.length > 0;
  }

  /**
   * Manage automatic freebie addition/removal - DISABLED
   * Only manual freebie selection is allowed
   */
  async manageAutomaticFreebies(_tab: BillingTab): Promise<void> {
    // Automatic freebie addition is disabled
    // Freebies can only be added manually by user selection
    return;
  }

  /**
   * Add a freebie to the cart
   */
  addFreebie(tab: BillingTab, freebie: FreebieProduct): void {
    const freebieItem = this.freebieService.createFreebieCartItem(freebie);
    tab.items.push(freebieItem);
    tab.addedFreebies.add(freebie.id);
  }

  /**
   * Remove all freebies from cart
   */
  removeAllFreebies(tab: BillingTab): void {
    const initialLength = tab.items.length;
    tab.items = this.freebieService.removeAllFreebies(tab.items);
    
    if (tab.items.length < initialLength) {
      tab.addedFreebies.clear();
    }
  }

  /**
   * Add freebie manually (from freebies table)
   */
  addFreebieManually(tab: BillingTab, freebie: FreebieProduct): boolean {
    if (!this.freebieService.canAddFreebie(tab.currentCartAmount, freebie, tab.items)) {
      return false;
    }

    // Only add the freebie - don't remove existing ones automatically
    this.addFreebie(tab, freebie);
    return true;
  }

  /**
   * Handle item removal (check if it's a freebie)
   */
  handleItemRemoval(tab: BillingTab, removedItem: any): void {
    if (removedItem.is_freebie && removedItem.freebie_id) {
      tab.addedFreebies.delete(removedItem.freebie_id);
    }
  }

  /**
   * Check and update freebies when cart amount changes
   */
  async checkCartAmountAndUpdate(tab: BillingTab, newCartAmount: number): Promise<boolean> {
    if (newCartAmount !== tab.currentCartAmount) {
      tab.currentCartAmount = newCartAmount;

      // If cart amount is 0, clear freebies and hide display
      if (newCartAmount === 0) {
        this.clearFreebiesOnCartClear(tab);
      } else {
        await this.updateFreebies(tab);
      }
      return true;
    }
    return false;
  }

  /**
   * Clear all freebies when cart is cleared
   */
  clearFreebiesOnCartClear(tab: BillingTab): void {
    this.removeAllFreebies(tab);
    tab.freebiesProducts = [];
    tab.showFreebies = false;
    tab.currentCartAmount = 0;
  }
}
